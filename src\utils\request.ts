import { getSessionStorage, handlingErrors } from '~/utils/utils'

const defaultOptions = {
  method: 'GET',
  credentials: 'include', // 携带跨域Cookie
  headers: {
    'Content-Type': 'application/json',
  },
}

async function handleResponse(response: Response, isThrowErr: boolean) {
  if (!response.ok) {
    handlingErrors(new Error(`${response.status} ${response.statusText}`))
    return
  }

  const data = await response.json()
  if (isThrowErr) {
    return data
  }
  else if (data?.code !== 1) {
    handlingErrors(new Error(`${data.msg}`))
    return
  }

  return data
}

export const httpWithErr = async function customFetch(url: string, options?: any, isThrowErr = true) {
  // 合并传入的选项和默认选项
  options = { ...defaultOptions, ...options }
  try {
    const response = await fetch(url, options)
    return handleResponse(response, isThrowErr)
  }
  catch (error) {
    handlingErrors(error)
  }
}

export const http = async function customFetch(url: string, options?: any) {
  return httpWithErr(url, options, false)
}

// 专门用于需要session认证的请求，确保携带必要的cookie
export const httpWithSession = async function sessionFetch(url: string, options?: any, isThrowErr = true) {
  // 合并传入的选项和默认选项
  options = { ...defaultOptions, ...options }

  // 确保headers对象存在
  if (!options.headers)
    options.headers = {}

  // 从sessionStorage获取token并构建cookie字符串
  const token = getSessionStorage('currToken')
  if (token) {
    // 构建完整的cookie字符串，包含session认证和必要的开关cookie
    const cookieString = `ts_session_id=${token}; ts_session_id_=${token}; m_gray_switch=1; m_gray_switch_=1`
    options.headers.Cookie = cookieString
  }
  else {
    // 如果没有token，至少设置必要的开关cookie
    options.headers.Cookie = 'm_gray_switch=1; m_gray_switch_=1'
  }

  try {
    const response = await fetch(url, options)
    return handleResponse(response, isThrowErr)
  }
  catch (error) {
    handlingErrors(error)
  }
}
